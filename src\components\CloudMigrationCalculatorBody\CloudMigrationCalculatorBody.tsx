'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import styles from './CloudMigrationCalculatorBody.module.css';
import useMediaQueryState from '@hooks/useMediaQueryState';

import QuestionAndAnswers from '@components/QuestionAndAnswers';
import Heading from '@components/Heading';
import Button from '@components/Button';
import HeroSection from '@components/HeroSection';
import CloudMigrationStep from '@components/CloudMigrationStep';
import CloudMigrationForm from '@components/CloudMigrationForm';
import Image from 'next/image';
import breakpoints from '@styles/breakpoints.module.css';

interface MigrationResult {
  totalCost: number;
  upperRange: number;
  costBreakdown: Record<string, number>;
}

interface CloudMigrationCalculatorBodyProps {
  body: any;
  formData: any;
}

export default function CloudMigrationCalculatorBody({
  body,
  formData,
}: CloudMigrationCalculatorBodyProps) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [visibleSection, setVisibleSection] = useState(null);
  const [result, setResult] = useState(null);
  const [URL, setURL] = useState([]);
  const router = useRouter();

  const isTablet = useMediaQueryState({
    query: `(max-width: 700px)`,
  });

  useEffect(() => {
    let newURL = [...URL];
    for (let i = 0; i < body?.cloud_migration_components?.data.length; i++) {
      newURL.push(
        body?.cloud_migration_components?.data[i].attributes.heading
          .toLowerCase()
          .replaceAll(' ', '-')
          .replaceAll('&', 'and'),
      );
    }
    newURL.push('result');
    setURL(newURL);

    initializeStates();

    function handleHashChange() {
      let currentHash = location.hash.substring(1);

      if (
        (currentHash === newURL[newURL.length - 2] &&
          localStorage.getItem('cloudMigrationResult') !== null) ||
        (currentHash === newURL[newURL.length - 1] &&
          localStorage.getItem('cloudMigrationResult') === null)
      ) {
        handleRestart();
      } else {
        handleVisibleSection(newURL.indexOf(currentHash));
      }
    }

    addEventListener('hashchange', handleHashChange);
    return () => removeEventListener('hashchange', handleHashChange);
  }, []);

  useEffect(() => {
    if (visibleSection !== null && URL.length > 0) {
      let url = '';
      if (visibleSection < body?.cloud_migration_components?.data.length) {
        url = URL[visibleSection];
      } else {
        url = 'result';
      }
      router.push('#' + url, { scroll: false });

      const element = document.getElementById(body?.hero_section?.button_link);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [visibleSection, URL]);

  function initializeStates() {
    let newData = [];
    let newError = [];
    let newVisibleSection = 0;
    let newResult = null;

    if (localStorage.getItem('cloudMigrationResult') !== null) {
      newResult = JSON.parse(localStorage.getItem('cloudMigrationResult'));
    }

    if (
      localStorage.getItem('cloudMigrationData') !== null &&
      localStorage.getItem('cloudMigrationError') !== null
    ) {
      newData = JSON.parse(localStorage.getItem('cloudMigrationData'));
      newError = JSON.parse(localStorage.getItem('cloudMigrationError'));
    } else {
      for (let i = 0; i < body?.cloud_migration_components?.data.length; i++) {
        let arrData = [];
        let arrError = [];

        for (
          let j = 0;
          j <
          body?.cloud_migration_components?.data[i]?.attributes?.questions
            .length;
          j++
        ) {
          const question =
            body?.cloud_migration_components?.data[i]?.attributes?.questions[j];
          if (question.type === 'mcq' || question.type === 'checkbox') {
            arrData.push([null, null]);
            arrError.push(null);
          } else if (question.type === 'range') {
            arrData.push([question.answers[0].name, question.answers[0].value]);
            arrError.push(false);
          }
        }
        newData[i] = arrData;
        newError[i] = arrError;
      }
    }

    if (localStorage.getItem('cloudMigrationVisibleSection') !== null) {
      newVisibleSection = JSON.parse(
        localStorage.getItem('cloudMigrationVisibleSection'),
      );
    }

    setData(newData);
    setError(newError);
    setVisibleSection(newVisibleSection);
    setResult(newResult);
  }

  function handleData(sectionIndex, questionIndex, name, value) {
    const newData = [...data];
    newData[sectionIndex][questionIndex][0] = name;
    newData[sectionIndex][questionIndex][1] = value;
    localStorage.setItem('cloudMigrationData', JSON.stringify(newData));
    setData(newData);
  }

  function handleError(sectionIndex, questionIndex, value) {
    const newError = [...error];
    newError[sectionIndex][questionIndex] = value;
    localStorage.setItem('cloudMigrationError', JSON.stringify(newError));
    setError(newError);
  }

  function handleNext() {
    if (canGoToNext()) {
      handleVisibleSection(visibleSection + 1);
    }
  }

  function handleRestart() {
    localStorage.removeItem('cloudMigrationData');
    localStorage.removeItem('cloudMigrationError');
    localStorage.removeItem('cloudMigrationVisibleSection');
    localStorage.removeItem('cloudMigrationResult');
    router.push(
      '/cloud-migration-cost-calculator#' +
        body?.cloud_migration_components?.data[0].attributes.heading
          .toLowerCase()
          .replaceAll(' ', '-')
          .replaceAll('&', 'and'),
    );
    initializeStates();
  }

  function handleVisibleSection(value) {
    if (value === -1) {
      setVisibleSection(data?.length || 0);
    } else {
      setVisibleSection(value);
    }
    localStorage.setItem(
      'cloudMigrationVisibleSection',
      JSON.stringify(value === -1 ? data?.length || 0 : value),
    );
  }

  function handlePrevious() {
    if (visibleSection > 0) {
      handleVisibleSection(visibleSection - 1);
    }
  }

  function canGoToNext() {
    if (data && error && visibleSection < data.length) {
      for (let i = 0; i < error[visibleSection].length; i++) {
        if (
          error[visibleSection][i] === true ||
          error[visibleSection][i] === null
        ) {
          return false;
        }
      }
    }
    return true;
  }

  // Cloud Migration Cost Calculation Logic based on spreadsheet
  function calculateMigrationCost() {
    if (!data || !canGoToNext()) return { data, newResult: {} };

    let totalCost = 0;
    let costBreakdown = {};
    let serverCost = 0;
    let storageCost = 0;

    // First pass: Calculate base infrastructure costs (servers and storage)
    for (let i = 0; i < data.length; i++) {
      const sectionData = data[i];
      const sectionName =
        body?.cloud_migration_components?.data[i]?.attributes?.heading;

      for (let j = 0; j < sectionData.length; j++) {
        const answer = sectionData[j];
        const questionData =
          body?.cloud_migration_components?.data[i]?.attributes?.questions[j];

        if (answer[1] !== null && answer[1] !== undefined) {
          const questionName = questionData.name.toLowerCase();

          // Store server and storage costs for high availability calculation
          if (questionName.includes('servers')) {
            serverCost = calculateQuestionCost(questionData, answer[1]);
          } else if (
            questionName.includes('capacity') ||
            questionName.includes('storage')
          ) {
            storageCost = calculateQuestionCost(questionData, answer[1]);
          }
        }
      }
    }

    // Second pass: Calculate all costs including high availability
    for (let i = 0; i < data.length; i++) {
      const sectionData = data[i];
      const sectionName =
        body?.cloud_migration_components?.data[i]?.attributes?.heading;

      for (let j = 0; j < sectionData.length; j++) {
        const answer = sectionData[j];
        const questionData =
          body?.cloud_migration_components?.data[i]?.attributes?.questions[j];

        if (answer[1] !== null && answer[1] !== undefined) {
          // Apply cost calculation based on question type and answer
          const cost = calculateQuestionCost(
            questionData,
            answer[1],
            serverCost,
            storageCost,
          );
          totalCost += cost;

          if (cost > 0) {
            costBreakdown[`${sectionName}_${questionData.name}`] = cost;
          }
        }
      }
    }

    const newResult = {
      totalCost: Math.round(totalCost),
      upperRange: Math.round(totalCost * 1.3), // 30% higher for upper range
      costBreakdown: costBreakdown,
    };

    localStorage.setItem('cloudMigrationResult', JSON.stringify(newResult));
    setResult(newResult);
    return { data, newResult };
  }

  function calculateQuestionCost(
    question,
    answerValue,
    serverCost = 0,
    storageCost = 0,
  ) {
    // Cost calculation logic based on the spreadsheet
    const questionName = question.name.toLowerCase();

    if (questionName.includes('servers')) {
      // Server count cost calculation
      if (answerValue <= 10) return 10000;
      if (answerValue <= 50) return 50000;
      if (answerValue <= 100) return 250000;
      return 500000;
    }

    if (questionName.includes('capacity') || questionName.includes('storage')) {
      // Storage capacity cost calculation
      if (answerValue <= 50) return 100;
      if (answerValue <= 200) return 500;
      if (answerValue <= 1000) return 2000;
      if (answerValue <= 10000) return 10000;
      if (answerValue <= 50000) return 50000;
      return 150000;
    }

    if (
      questionName.includes('availability') ||
      questionName.includes('disaster')
    ) {
      // High availability cost (20% of infrastructure + data costs)
      // If user selects "Yes" (answerValue = 1), add 20% of combined server and storage costs
      // If user selects "No" (answerValue = 0), add 0%
      if (answerValue === 1) {
        const combinedInfrastructureCost = serverCost + storageCost;
        return Math.round(combinedInfrastructureCost * 0.2);
      }
      return 0;
    }

    if (questionName.includes('environment')) {
      // Environment costs
      if (questionName.includes('dev')) return 10000;
      if (questionName.includes('test')) return 15000;
      if (questionName.includes('staging')) return 20000;
      if (questionName.includes('production')) return 100000;
    }

    if (questionName.includes('compliance')) {
      // Compliance costs based on type
      const complianceMap = {
        hipaa: 20000,
        gdpr: 10000,
        'pci dss': 15000,
        'soc 2': 15000,
        ccpa: 5000,
        fedramp: 50000,
      };
      return complianceMap[question.name.toLowerCase()] || 0;
    }

    if (questionName.includes('strategy')) {
      // Migration strategy costs
      const strategyMap = {
        'lift-and-shift': 5000,
        're-platforming': 50000,
        're-architecting': 150000,
        hybrid: 100000,
      };
      return strategyMap[question.name.toLowerCase()] || 0;
    }

    if (questionName.includes('auto-scaling')) {
      return answerValue === 1 ? 10000 : 0; // Yes = 1, No = 0
    }

    return 0;
  }

  function handleResult() {
    return calculateMigrationCost();
  }

  return (
    <>
      {data && visibleSection < data.length && (
        <HeroSection heroData={body?.hero_section} variant="cloud-migration" />
      )}
      {data && (
        <div className={styles.container} id={body?.hero_section?.button_link}>
          {visibleSection < data.length && (
            <>
              {isTablet ? (
                <>
                  <div className={styles.button_wrapper_mobile}>
                    {visibleSection > 0 && visibleSection < data.length && (
                      <button onClick={handlePrevious}>
                        <Image
                          src="https://cdn.marutitech.com/black_chevron_left_5adc2eb9de.svg"
                          alt="previous section"
                          width={25}
                          height={25}
                        />
                      </button>
                    )}
                    {visibleSection + 1}/0{URL.length - 1}
                    {visibleSection < data.length - 1 && (
                      <button
                        onClick={() => {
                          if (canGoToNext())
                            handleVisibleSection(visibleSection + 1);
                        }}
                      >
                        <Image
                          src="https://cdn.marutitech.com/black_chevron_left_6d81dc24e5.svg"
                          alt="next section"
                          width={25}
                          height={25}
                        />
                      </button>
                    )}
                  </div>
                </>
              ) : (
                <CloudMigrationStep
                  visibleCount={visibleSection}
                  onStepClick={stepNumber =>
                    handleVisibleSection(stepNumber - 1)
                  }
                />
              )}
            </>
          )}

          {body?.cloud_migration_components?.data.map(
            (section, sectionIndex) => (
              <div
                key={sectionIndex}
                className={
                  visibleSection === sectionIndex
                    ? styles.section_wrapper
                    : styles.hidden
                }
              >
                <div className={styles.heading}>
                  <h2>
                    {sectionIndex + 1}. {section?.attributes?.heading}
                  </h2>
                </div>
                {visibleSection !== data.length && (
                  <QuestionAndAnswers
                    sectionIndex={sectionIndex}
                    sectionQuestions={section?.attributes.questions}
                    sectionData={data[sectionIndex]}
                    sectionError={error[sectionIndex]}
                    handleData={handleData}
                    handleError={handleError}
                  />
                )}
                <span id="error">
                  {visibleSection < data.length &&
                    error[visibleSection].includes(true) && (
                      <div className={styles.error_message}>
                        Please fill all the required fields.
                      </div>
                    )}
                </span>

                {visibleSection === data.length - 1 && (
                  <CloudMigrationForm
                    formData={formData}
                    handleResult={handleResult}
                    handleVisibleSection={handleVisibleSection}
                  />
                )}
                <div className={styles.button_wrapper}>
                  {visibleSection > 0 && visibleSection < data.length && (
                    <button onClick={handlePrevious}>
                      <Image
                        src="https://cdn.marutitech.com/chevron_left_7f3e8fa9d6.svg"
                        alt="previous section"
                        width={25}
                        height={25}
                      />
                      Previous
                    </button>
                  )}
                  {visibleSection < data.length - 1 && (
                    <button
                      onClick={() => {
                        if (canGoToNext())
                          handleVisibleSection(visibleSection + 1);
                      }}
                    >
                      Next
                      <Image
                        src="https://cdn.marutitech.com/chevron_right_7f3e8fa9d6.svg"
                        alt="next section"
                        width={25}
                        height={25}
                      />
                    </button>
                  )}
                </div>
              </div>
            ),
          )}
        </div>
      )}

      {data && visibleSection === data.length && result && (
        <div className={styles.container}>
          <div className={styles.result_section}>
            <div className={styles.cost_display}>
              <h2>Your Estimated Cloud Migration Cost</h2>
              <div className={styles.cost_range}>
                <span className={styles.cost_amount}>
                  ${result.totalCost?.toLocaleString()} – $
                  {result.upperRange?.toLocaleString()} USD
                </span>
              </div>
              <p className={styles.disclaimer}>
                Disclaimer: This estimate is a ballpark figure derived from
                typical migration patterns and industry benchmarks. Actual costs
                can vary depending on your unique setup, complexity needs,
                application dependencies, and more nuanced business and
                technical requirements.
              </p>
            </div>

            <div className={styles.restart_section}>
              <Button
                className={styles.restart_button}
                label={body?.restart_button?.title || 'Restart Assessment'}
                type="button"
                onClick={handleRestart}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
}
