import { notFound } from 'next/navigation';
import CloudMigrationCalculatorBody from '@components/CloudMigrationCalculatorBody';
import seoSchema from '@utils/seoSchema';

import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

async function getCloudMigrationCalculatorData() {
  return await fetchFromStrapi(
    'cloud-migration-cost-calculator',
    'populate=hero_section.image,hero_section.mobile_image,cloud_migration_components.questions.answers,cloud_migration_components.questions.sub_question,form.formFields,form.button,restart_button,consultation_button,seo.schema',
  );
}

async function getFormData() {
  const query = `populate=form.formFields&populate=form.button`;
  return await fetchFromStrapi('form', query);
}

export async function generateMetadata({}) {
  const seoFetchedData = await fetchFromStrapi(
    'cloud-migration-cost-calculator',
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema',
  );
  const seoData = seoFetchedData?.data?.attributes?.seo;

  return seoSchema(seoData);
}

export default async function CloudMigrationCostCalculator() {
  const cloudMigrationData = await getCloudMigrationCalculatorData();
  const formData = await getFormData();

  if (!cloudMigrationData?.data || cloudMigrationData?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {cloudMigrationData?.data?.attributes?.seo && (
        <RichResults data={cloudMigrationData?.data?.attributes?.seo} />
      )}
      {cloudMigrationData?.data?.attributes && (
        <CloudMigrationCalculatorBody
          body={cloudMigrationData?.data?.attributes}
          formData={formData?.data?.attributes?.form}
        />
      )}
    </>
  );
}
